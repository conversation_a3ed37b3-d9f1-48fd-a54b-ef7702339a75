const RubyConverter = {
  // ----------------------------------------------------------------
  // § 1. 配置与核心属性 (Configuration & Core Properties)
  // ----------------------------------------------------------------
  _config: {
    MODULE_ENABLED: true,
  },
  // 原始规则配置 (将由 init 传入)
  _rawConfig: null,
  // 正则表达式集合
  _regex: {
    bracket: /[【「]([^【】「」（）・、\s～〜]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
    katakana: /([ァ-ンー]+)[（(]([\w\s+]+)[）)]/g,
    ruby: /([一-龯々]+)\s*[(（]([^（）()]*)[)）]/g,
    kanaOnly: /^[\u3040-\u309F]+$/,
    nonKana: /[^\u3040-\u309F]/,
    isKanaChar: /^[\u3040-\u309F\u30A0-\u30FF]$/,
    hasInvalidChars: /[^一-龯々\u3040-\u309F\u30A0-\u30FF]/,
  },
  // --- 以下为“词库引擎”的核心数据结构 ---
  // 已注册词条的 Set，用于高效去重
  _registeredWords: new Set(),
  // 用于构建最终 globalRegex 的词条模式源
  _wordBankForRegex: [],
  // 预处理好的 Ruby 结果缓存，我们的“最终词库”
  _rubyCache: new Map(),
  // 预编译好的 HTML 补丁集，用于修复特殊 HTML 结构
  _htmlPatches: new Map(),
  // 最终生成的“超级搜索引擎”正则
  globalRegex: null,

  // ----------------------------------------------------------------
  // § 2. 公共接口方法 (Public API Methods)
  // ----------------------------------------------------------------

  /**
   * 初始化引擎，这是外部调用的主入口。
   * 它将触发对所有静态规则的编译。
   * @param {object} rules - 原始的 RULES 对象
   */
  init(rules) {
    if (!this._config.MODULE_ENABLED) return
    this._rules = rules // 保持兼容性
    this._rawConfig = rules

    console.log(' FuranEngine: Compiling static rules...')
    this.compile()
    console.log(` FuranEngine: Compilation complete. ${this._registeredWords.size} words registered, ${this._htmlPatches.size} HTML patches created.`)
  },

  /**
   * 处理一个具体的页面DOM容器。
   * 这是第二阶段“页面处理”的入口。
   * 为了保持外部调用兼容性，我们保留 applyRubyToContainer 这个名称。
   * @param {HTMLElement} container - 需要处理的DOM元素
   */
  applyRubyToContainer(container) {
    if (!this._config.MODULE_ENABLED) return
    // 步骤 2.1: 应用HTML补丁
    this._applyHtmlPatches(container)
    // 步骤 2.2: 从容器中学习新词
    this._learnFromContainer(container)
    // 步骤 2.3: 整合所有规则，生成最终的正则
    this._buildFinalRegex()
    // 步骤 2.4: 对所有文本节点应用替换链
    this._processTextNodes(container)
  },

  // ----------------------------------------------------------------
  // § 3. 内部核心方法 (Internal Core Methods)
  // ----------------------------------------------------------------

  /**
   * 阶段一：编译。
   * 此阶段处理所有与页面内容无关的静态规则。
   */
  compile() {
    this._compileStaticRules()
    this._buildFinalRegex() // 根据静态规则先构建一次
  },

  /**
   * 编译所有来自 _rawConfig 的静态规则。
   * 它会区分哪些规则可以被注册进“词库”，哪些是复杂的“HTML补丁”。
   */
  _compileStaticRules() {
    // 处理 HTML 规则
    const { HTML, TEXT } = this._rawConfig
    // 1. 处理 HTML.FULL_REPLACE -> 它们总是 HTML 补丁
    HTML.FULL_REPLACE.forEach((rule) => {
      const pattern = typeof rule.pattern === 'string' ? new RegExp(this._escapeRegExp(rule.pattern), 'g') : rule.pattern
      this._htmlPatches.set(pattern, rule.replacement)
    })

    // 2. 处理 HTML.MANUAL_MARK 和 HTML.OVERRIDE_READING
    // 这些规则可能是一个标准词条，也可能是一个复杂的补丁
    const htmlRulesToProcess = [...HTML.MANUAL_MARK, ...(HTML.OVERRIDE_READING || [])]
    htmlRulesToProcess.forEach((rule) => {
      // 尝试将其解析为标准 `单词（读音）` 格式
      const patternString = typeof rule === 'object' ? rule.pattern : rule
      const readingString = typeof rule === 'object' ? rule.reading : patternString

      const textOnly = patternString.replace(/<[^>]+>|\?/g, '')
      const match = textOnly.match(/(.*?)（(.*?)）/)

      if (match && !this._regex.nonKana.test(match[2])) {
        // 如果可以被解析为一个标准词条，则尝试注册它
        this.registerWord(`${match[1]}（${match[2]}）`, 'Static.HTML')
      } else {
        // 否则，将其作为一个复杂的 HTML 补丁来处理
        // (此处的 patch 创建逻辑较为复杂，我们简化示意)
        const pattern = new RegExp(this._escapeRegExp(patternString).replace('\\?', '((?:<[^>]+>)*?)'), 'g')
        const readingMatch = readingString.match(/（(.*?)）/)
        if (readingMatch) {
          const rubyHtml = this._parseFurigana(textOnly.replace(/（.*?）/, ''), readingMatch[1])
          if (rubyHtml) {
            this._htmlPatches.set(pattern, (match, capturedTags) => rubyHtml + (capturedTags || ''))
          }
        }
      }
    })

    // 3. 处理 TEXT 规则 -> 它们总是尝试被注册到词库
    const textRulesToProcess = [...TEXT.MANUAL_MARK, ...TEXT.OVERRIDE_READING, ...TEXT.FULL_REPLACE]
    textRulesToProcess.forEach((rule) => {
      const pattern = typeof rule === 'object' ? rule.pattern : rule
      const replacement = typeof rule === 'object' ? rule.replacement : null
      if (replacement) {
        // 对于 FULL_REPLACE，直接存入缓存
        this.registerWord(pattern, 'Static.TEXT.FULL_REPLACE', replacement)
      } else {
        this.registerWord(pattern, 'Static.TEXT')
      }
    })
  },

  /**
   * 统一的、权威的“词条注册”方法。
   * @param {string} pattern - 格式为 `单词（读音）` 的字符串
   * @param {string} source - 规则来源，用于日志输出
   * @param {string} [overrideReplacement] - 可选的、直接指定的替换结果
   */
  registerWord(pattern, source, overrideReplacement = null) {
    // 步骤 1: 去重检查
    if (this._registeredWords.has(pattern)) {
      return // 已注册，直接返回
    }

    // 对于直接指定替换结果的规则 (TEXT.FULL_REPLACE)
    if (overrideReplacement) {
      this._registeredWords.add(pattern)
      this._wordBankForRegex.push(this._escapeRegExp(pattern))
      this._rubyCache.set(pattern, overrideReplacement)
      console.log(`📖 [Registered] Source: ${source}, Word: "${pattern}" (Direct Replacement)`)
      return
    }

    // 步骤 2: 解析
    const match = pattern.match(/(.*?)（(.*?)）/)
    if (!match) return
    const [_, kanjiPart, readingPart] = match

    // 步骤 3: 核验
    const rubyHtml = this._parseFurigana(kanjiPart, readingPart)

    // 步骤 4: 优雅降级
    if (rubyHtml === null) {
      console.warn(`⚠️ [Validation Failed] Source: ${source}, Word: "${pattern}" will be skipped.`)
      return
    }

    // 步骤 5: 正式收录
    this._registeredWords.add(pattern)
    this._wordBankForRegex.push(this._escapeRegExp(pattern))
    this._rubyCache.set(pattern, rubyHtml)
    console.log(`📖 [Registered] Source: ${source}, Word: "${pattern}"`)
  },

  /**
   * 从容器中学习新词，并注册它们。
   * @param {HTMLElement} container
   */
  _learnFromContainer(container) {
    const html = container.innerHTML
    for (const match of html.matchAll(this._regex.bracket)) {
      const kanjiPart = match[1]
      const readingPart = match[2]
      const corePattern = `${kanjiPart}（${readingPart}）`

      // 进行一些基本检查
      if (this._regex.nonKana.test(readingPart) || this._regex.hasInvalidChars.test(kanjiPart)) {
        continue
      }

      this.registerWord(corePattern, 'Dynamic Learning')
    }
  },

  /**
   * 应用 HTML 补丁来修复特殊结构。
   * @param {HTMLElement} container
   */
  _applyHtmlPatches(container) {
    if (this._htmlPatches.size === 0) return
    let html = container.innerHTML
    const originalHtml = html
    this._htmlPatches.forEach((replacement, pattern) => {
      html = html.replace(pattern, replacement)
    })
    if (html !== originalHtml) {
      container.innerHTML = html
    }
  },

  /**
   * 根据当前的词库，构建或重建 globalRegex。
   */
  _buildFinalRegex() {
    this.globalRegex = this._wordBankForRegex.length > 0 ? new RegExp(`(${this._wordBankForRegex.join('|')})`, 'g') : null
  },

  /**
   * 遍历容器的所有文本节点，并应用替换链。
   * @param {HTMLElement} root
   */
  _processTextNodes(root) {
    const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: (n) => (n.parentNode.nodeName !== 'SCRIPT' && n.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
    })
    const nodesToProcess = []
    let node
    while ((node = walker.nextNode())) {
      const newContent = this._applyTextReplacements(node.nodeValue)
      if (newContent !== node.nodeValue) {
        nodesToProcess.push({ node, newContent })
      }
    }
    // 从后往前替换，避免影响 DOM 结构
    for (let i = nodesToProcess.length - 1; i >= 0; i--) {
      const { node, newContent } = nodesToProcess[i]
      const fragment = document.createRange().createContextualFragment(newContent)
      node.parentNode.replaceChild(fragment, node)
    }
  },

  /**
   * 对纯文本内容执行三步替换链。
   * @param {string} text
   * @returns {string}
   */
  _applyTextReplacements(text) {
    if (!text.includes('（') && !text.includes('(')) {
      return text
    }

    let processedText = text

    // 替换链 1: 高优先级词库匹配
    if (this.globalRegex) {
      processedText = processedText.replace(this.globalRegex, (match) => {
        return this._rubyCache.get(match) || match
      })
    }

    // 替换链 2: 片假名模式即时匹配
    processedText = processedText.replace(this._regex.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`)

    // 替换链 3: 通用汉字模式后备匹配 (含排除检查)
    processedText = processedText.replace(this._regex.ruby, (match, kanji, reading) => {
      const fullMatch = `${kanji}（${reading}）`
      if (this._rawConfig.EXCLUDE.STRINGS.has(fullMatch) || this._regex.nonKana.test(reading)) {
        return match // 满足排除条件，不转换
      }
      return `<ruby>${kanji}<rt>${reading}</rt></ruby>`
    })

    return processedText
  },

  // ----------------------------------------------------------------
  // § 4. 工具/辅助方法 (Utility/Helper Methods)
  // ----------------------------------------------------------------

  _parseFurigana(kanji, reading) {
    const hiraganaReading = this._katakanaToHiragana(reading)
    let result = ''
    let kanjiIndex = 0
    let readingIndex = 0
    while (kanjiIndex < kanji.length) {
      const currentKanjiChar = kanji[kanjiIndex]
      if (this._regex.isKanaChar.test(currentKanjiChar)) {
        result += currentKanjiChar
        const hiraganaCurrent = this._katakanaToHiragana(currentKanjiChar)
        const tempNextReadingIndex = hiraganaReading.indexOf(hiraganaCurrent, readingIndex)
        if (tempNextReadingIndex !== -1) {
          readingIndex = tempNextReadingIndex + hiraganaCurrent.length
        } else {
          return null // 严重错误：假名在读音中不匹配
        }
        kanjiIndex++
      } else {
        let kanjiPart = ''
        let blockEndIndex = kanjiIndex
        while (blockEndIndex < kanji.length && !this._regex.isKanaChar.test(kanji[blockEndIndex])) {
          kanjiPart += kanji[blockEndIndex]
          blockEndIndex++
        }
        const nextKanaInKanji = kanji[blockEndIndex]
        let readingEndIndex
        if (nextKanaInKanji) {
          const hiraganaNextKana = this._katakanaToHiragana(nextKanaInKanji)
          readingEndIndex = hiraganaReading.indexOf(hiraganaNextKana, readingIndex)
          if (readingEndIndex === -1) {
            readingEndIndex = hiraganaReading.length
          }
        } else {
          readingEndIndex = hiraganaReading.length
        }
        const readingPart = reading.substring(readingIndex, readingEndIndex)
        if (kanjiPart) {
          if (!readingPart) {
            return null // 严重错误：汉字部分没有对应的读音
          } else {
            result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`
          }
        }
        readingIndex = readingEndIndex
        kanjiIndex = blockEndIndex
      }
    }
    // 最后检查是否有多余的读音未被使用
    if (readingIndex < hiraganaReading.length) {
      // 在某些情况下，末尾的送假名可能在kanji部分省略，这里可以根据需要放宽或收紧
      // 为保持严谨，我们暂时也视为一种潜在问题
      // console.warn(`[WARN] Trailing reading characters found for "${kanji}"`);
    }
    return result
  },

  _katakanaToHiragana(str) {
    if (!str) return ''
    return str.replace(/[\u30A1-\u30F6]/g, (match) => String.fromCharCode(match.charCodeAt(0) - 0x60))
  },

  _escapeRegExp: (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
}
