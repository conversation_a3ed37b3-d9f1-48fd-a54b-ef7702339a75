/**
 * @module TTSPlayer
 * @description 通过外部API播放在线生成的日语语音 (v4: Multi-Source)
 */
const TTSPlayer = {
  _config: {
    // ⬇️ 核心改动：API现在是一个数组，用于故障切换
    API_DOMAINS: ['https://anki.0w0.live/api/aiyue', 'https://ms-ra-forwarder-for-ifreetime-anki-japan-demo.vercel.app/api/aiyue'],
    VOICE_NAMES: ['ja-JP-KeitaNeural', 'ja-JP-NanamiNeural'],
    SPEED: -4,
  },
  audioElement: null,

  speak(text) {
    if (!text || typeof text !== 'string') return

    if (this.audioElement && !this.audioElement.paused) {
      this.audioElement.pause()
      this.audioElement.currentTime = 0
      return
    }

    const randomVoice = this._config.VOICE_NAMES[Math.floor(Math.random() * this._config.VOICE_NAMES.length)]
    const params = new URLSearchParams()
    params.append('voiceName', randomVoice)
    params.append('text', text)

    if (!this.audioElement) {
      this.audioElement = new Audio()
      this.audioElement.onerror = (e) => {
        // 当所有 <source> 都尝试失败后，会触发audio元素的error事件
        console.error('所有TTS API源均加载失败。', e)
      }
    }

    // ⬇️ 核心改动：动态创建多个 <source> 标签，将故障切换逻辑完全交给浏览器
    this.audioElement.innerHTML = '' // 播放前先清空旧的源
    this._config.API_DOMAINS.forEach((domain) => {
      const source = document.createElement('source')
      source.src = `${domain}?${params.toString()}`
      source.type = 'audio/mpeg'
      this.audioElement.appendChild(source)
    })

    this.audioElement.load() // 重要：让 <audio> 元素重新加载新的<source>列表
    this.audioElement.play()
  },
}
const ContextMenu = {
  _config: {
    MODULE_ENABLED: true,
    MENU_ID: 'ip-selection-context-menu',
    TEXT_LENGTH_LIMIT: 200,
    STYLES: `
      #ip-selection-context-menu { position: absolute; display: none; z-index: 2147483647; opacity: 0; transform: scale(0.95); transition: transform 0.1s ease-out, opacity 0.1s ease-out; }
      #ip-selection-context-menu.visible { opacity: 0.9; transform: scale(1); }
      #ip-selection-context-menu button { display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; padding: 0; border-radius: 50%; cursor: pointer; border: 1px solid #dcdcdc; background-color: #f8f9fa; color: #5f6368; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: all 0.2s; }
      #ip-selection-context-menu button:hover { border-color: #3B82F6; color: #3B82F6; transform: scale(1.1); }
      #ip-selection-context-menu button svg { width: 18px; height: 18px; stroke: currentColor; stroke-width: 2.5; }
    `,
  },
  menuElement: null,
  init() {
    if (!this._config.MODULE_ENABLED) return
    GM_addStyle(this._config.STYLES)
    this._createMenu()
    this._bindEvents()
  },
  _createMenu() {
    if (document.getElementById(this._config.MENU_ID)) return
    this.menuElement = document.createElement('div')
    this.menuElement.id = this._config.MENU_ID

    const readButton = document.createElement('button')
    readButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>`

    readButton.addEventListener('click', (event) => {
      event.stopPropagation()
      const cleanedText = this._getCleanedSelectedText() // 调用新的清理方法
      if (cleanedText) {
        TTSPlayer.speak(cleanedText)
      }
    })

    this.menuElement.appendChild(readButton)
    document.body.appendChild(this.menuElement)
  },

  /**
   * 新增的辅助方法，用于获取并深度清理选中的文本内容
   * @private
   */
  _getCleanedSelectedText() {
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return ''

    const selectedFragment = selection.getRangeAt(0).cloneContents()
    const tempContainer = document.createElement('div')
    tempContainer.appendChild(selectedFragment)

    // 1. 移除所有 <rt> (注音) 元素
    tempContainer.querySelectorAll('rt').forEach((el) => el.remove())

    // 2. 将所有 <br> 替换为句号
    tempContainer.querySelectorAll('br').forEach((el) => el.replaceWith('。'))

    // 3. 剥离所有纯样式包裹标签 (span, b, strong, i, em)，保留其内部文本
    // querySelectorAll返回的是静态列表，可以直接遍历并修改
    tempContainer.querySelectorAll('ruby, span, b, strong, i, em, font').forEach((el) => {
      // 用元素的所有子节点替换该元素本身
      el.replaceWith(...el.childNodes)
    })

    // 4. 返回处理干净后的纯文本
    return tempContainer.textContent.replace(/\s+/g, ' ') // 将多个空白符合并为一个空格
  },

  _bindEvents() {
    document.addEventListener('mouseup', this._handleMouseUp.bind(this), true)
    document.addEventListener('mousedown', this._handleMouseDown.bind(this), true)
  },
  _handleMouseUp(event) {
    if (!this.menuElement || this.menuElement.contains(event.target)) return
    requestAnimationFrame(() => {
      const selectedText = window.getSelection().toString().trim()
      if (selectedText.length > 0) {
        const x = event.pageX
        const y = event.pageY
        this._showMenu(x, y)
      } else {
        this._hideMenu()
      }
    })
  },
  _handleMouseDown(event) {
    if (this.menuElement && !this.menuElement.contains(event.target)) {
      this._hideMenu()
    }
  },
  _showMenu(x, y) {
    if (!this.menuElement) return
    this.menuElement.style.left = `${x + 5}px`
    this.menuElement.style.top = `${y + 5}px`
    this.menuElement.style.display = 'block'
    setTimeout(() => this.menuElement.classList.add('visible'), 10)
  },
  _hideMenu() {
    if (!this.menuElement) return
    this.menuElement.classList.remove('visible')
    setTimeout(() => {
      if (!this.menuElement.classList.contains('visible')) {
        this.menuElement.style.display = 'none'
      }
    }, 150)
  },
}
